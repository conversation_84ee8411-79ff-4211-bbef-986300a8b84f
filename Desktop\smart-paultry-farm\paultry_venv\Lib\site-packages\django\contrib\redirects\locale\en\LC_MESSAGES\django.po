# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/redirects/apps.py:8
msgid "Redirects"
msgstr ""

#: contrib/redirects/models.py:7
msgid "site"
msgstr ""

#: contrib/redirects/models.py:9
msgid "redirect from"
msgstr ""

#: contrib/redirects/models.py:12
msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""

#: contrib/redirects/models.py:15
msgid "redirect to"
msgstr ""

#: contrib/redirects/models.py:19
msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""

#: contrib/redirects/models.py:25
msgid "redirect"
msgstr ""

#: contrib/redirects/models.py:26
msgid "redirects"
msgstr ""
