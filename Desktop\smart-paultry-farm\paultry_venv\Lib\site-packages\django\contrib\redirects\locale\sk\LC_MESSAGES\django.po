# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2017,2023
# <PERSON>, 2021
# supo<PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 18:32+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017,2023\n"
"Language-Team: Slovak (http://app.transifex.com/django/django/language/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

msgid "Redirects"
msgstr "Presmerovania"

msgid "site"
msgstr "sídlo"

msgid "redirect from"
msgstr "presmerovať z"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Toto by sa mala byť absolútna cesta bez názvu domény. Napríklad: „/events/"
"search/“."

msgid "redirect to"
msgstr "presmerovať na"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Toto môže byť buď absolútna cesta (ako vyššie), alebo úplné URL začínajúce "
"schémou ako „https://“."

msgid "redirect"
msgstr "presmerovanie"

msgid "redirects"
msgstr "presmerovania"
