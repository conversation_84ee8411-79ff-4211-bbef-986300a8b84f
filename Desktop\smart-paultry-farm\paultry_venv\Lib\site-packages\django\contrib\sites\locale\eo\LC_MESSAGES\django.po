# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Retejoj"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Tiu domajna nomo ne povas enteni nek spacojn nek tabojn."

msgid "domain name"
msgstr "domajna nomo"

msgid "display name"
msgstr "vidiga nomo"

msgid "site"
msgstr "retejo"

msgid "sites"
msgstr "retejoj"
